<template>
</template>
<script>
import fileDialogService from '@/utils/fileDialogService';
export default {
  name: '',
  components: {},
  props: {},
  data () {
    return {}
  },
  mounted () {},
  methods: {
    initData(value){
      // 使用新的文件对话服务
      try {
        const files = value.map(v => v.userDocName);
        const docIds = value.map(v => v.docId).join(",");

        // 启动文件对话（不设置状态组件引用，让 FileDialogStatus 组件处理）
        fileDialogService.startFileDialog(files, docIds);
      } catch (error) {
        console.error('启动文件对话失败:', error);
        this.$message.error(error.message || '启动文件对话失败');
      }
    }


  }
}
</script>

<style scoped lang="scss">
</style>
